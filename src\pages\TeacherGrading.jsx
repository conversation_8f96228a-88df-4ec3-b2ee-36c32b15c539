import React, { useState, useEffect, useContext, useCallback } from 'react';
import { Box, Text, Button, useNavigate, Input, Select, List } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import LoadingIndicator from '../components/LoadingIndicator';
import DateInput from '../components/DateInput';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';

const { Option } = Select;

const TeacherGrading = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);

    // State for exams data
    const [exams, setExams] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10
    });

    // Filter states
    const [filters, setFilters] = useState({
        startDateFrom: '2025-01-01',
        startDateTo: '2025-06-30',
        status: 'active',
        page: 1,
        limit: 10
    });

    // Fetch exams data
    const fetchExams = useCallback(async () => {
        if (!user) return;
        
        setLoading(true);
        try {
            const params = new URLSearchParams({
                startDateFrom: filters.startDateFrom,
                startDateTo: filters.startDateTo,
                status: filters.status,
                page: filters.page.toString(),
                limit: filters.limit.toString()
            });

            const response = await authApi.get(`/exams/my-exams?${params}`);
            
            if (response.data.success) {
                setExams(response.data.data || []);
                setPagination(response.data.pagination || {
                    currentPage: 1,
                    totalPages: 1,
                    totalItems: 0,
                    itemsPerPage: 10
                });
            }
        } catch (error) {
            console.error('Error fetching exams:', error);
        } finally {
            setLoading(false);
        }
    }, [user, filters]);

    // Handle filter changes
    const handleFilterChange = (field, value) => {
        setFilters(prev => ({
            ...prev,
            [field]: value,
            page: field !== 'page' ? 1 : value // Reset to page 1 when other filters change
        }));
    };

    // Handle pagination
    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= pagination.totalPages) {
            handleFilterChange('page', newPage);
        }
    };

    // Apply filters
    const applyFilters = () => {
        fetchExams();
    };

    // Reset filters
    const resetFilters = () => {
        setFilters({
            startDateFrom: '2025-01-01',
            startDateTo: '2025-06-30',
            status: 'active',
            page: 1,
            limit: 10
        });
    };

    // Navigate to exam results
    const viewExamResults = (examId) => {
        navigate(`/exam-results/${examId}`);
    };

    // Check authentication
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && !user.role.includes('teacher') && !user.role.includes('TEACHER')) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Fetch data when filters change
    useEffect(() => {
        fetchExams();
    }, [fetchExams]);

    // Calculate score color
    const getScoreColor = (score, maxScore = 10) => {
        const percentage = (score / maxScore) * 100;
        if (percentage >= 80) return '#4CAF50';
        if (percentage >= 60) return '#FF9800';
        return '#F44336';
    };

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu
                title="Chấm bài thi"
                subtitle="Quản lý và chấm điểm các bài thi của học sinh"
                showBackButton
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />
            
            {/* Filters */}
            <Box style={{ padding: '15px', backgroundColor: 'white', marginBottom: '10px' }}>
                <Text bold style={{ marginBottom: '15px' }}>Bộ lọc</Text>
                
                <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '10px', marginBottom: '15px' }}>
                    {/* Start Date From */}
                    <Box>
                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Từ ngày</Text>
                        <DateInput
                            value={filters.startDateFrom}
                            onChange={(e) => handleFilterChange('startDateFrom', e.target.value)}
                            style={{ fontSize: '14px' }}
                        />
                    </Box>

                    {/* Start Date To */}
                    <Box>
                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Đến ngày</Text>
                        <DateInput
                            value={filters.startDateTo}
                            onChange={(e) => handleFilterChange('startDateTo', e.target.value)}
                            style={{ fontSize: '14px' }}
                        />
                    </Box>
                </Box>

                <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '10px', marginBottom: '15px' }}>
                    {/* Status */}
                    <Box>
                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Trạng thái</Text>
                        <Select
                            value={filters.status}
                            onChange={(value) => handleFilterChange('status', value)}
                        >
                            <Option value="active" title="Đang hoạt động" />
                            <Option value="inactive" title="Ngừng hoạt động" />
                            <Option value="all" title="Tất cả" />
                        </Select>
                    </Box>

                    {/* Items per page */}
                    <Box>
                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Số lượng/trang</Text>
                        <Select
                            value={filters.limit.toString()}
                            onChange={(value) => handleFilterChange('limit', parseInt(value))}
                        >
                            <Option value="5" title="5" />
                            <Option value="10" title="10" />
                            <Option value="20" title="20" />
                            <Option value="50" title="50" />
                        </Select>
                    </Box>
                </Box>

                {/* Filter Actions */}
                <Box flex style={{ gap: '10px' }}>
                    <Button 
                        onClick={applyFilters}
                        style={{ flex: 1, backgroundColor: '#0068ff', color: 'white' }}
                    >
                        Áp dụng
                    </Button>
                    <Button 
                        onClick={resetFilters}
                        style={{ flex: 1, backgroundColor: '#f0f0f0', color: '#333' }}
                    >
                        Đặt lại
                    </Button>
                </Box>
            </Box>

            {/* Exams List */}
            <Box style={{ flex: 1, backgroundColor: 'white', padding: '15px' }}>
                <Box flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text bold size="large">Danh sách bài thi ({pagination.totalItems})</Text>
                    <Button
                        onClick={fetchExams}
                        style={{ backgroundColor: 'transparent', color: '#0068ff', fontSize: '20px' }}
                    >
                        🔄
                    </Button>
                </Box>

                {loading ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : exams.length > 0 ? (
                    <List style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
                        {exams.map((exam) => (
                            <Box 
                                key={exam._id}
                                style={{
                                    backgroundColor: '#f9f9f9',
                                    borderRadius: '12px',
                                    padding: '15px',
                                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                                    cursor: 'pointer',
                                    transition: 'transform 0.2s',
                                }}
                                onClick={() => viewExamResults(exam._id)}
                            >
                                {/* Exam Header */}
                                <Box flex justifyContent="space-between" alignItems="flex-start" style={{ marginBottom: '10px' }}>
                                    <Box style={{ flex: 1 }}>
                                        <Text bold size="large" style={{ marginBottom: '5px' }}>
                                            {exam.title}
                                        </Text>
                                        <Box flex alignItems="center" style={{ gap: '10px', marginBottom: '5px' }}>
                                            <Box style={{
                                                backgroundColor: '#e8f0fe',
                                                color: '#0068ff',
                                                padding: '2px 8px',
                                                borderRadius: '12px',
                                                fontSize: '12px',
                                                fontWeight: 'bold'
                                            }}>
                                                {exam.subject?.name}
                                            </Box>
                                            <Box style={{
                                                backgroundColor: exam.isActive ? '#e8f5e8' : '#ffe8e8',
                                                color: exam.isActive ? '#4CAF50' : '#F44336',
                                                padding: '2px 8px',
                                                borderRadius: '12px',
                                                fontSize: '12px',
                                                fontWeight: 'bold'
                                            }}>
                                                {exam.isActive ? 'Hoạt động' : 'Ngừng'}
                                            </Box>
                                        </Box>
                                    </Box>
                                    <Text style={{ fontSize: '24px' }}>📊</Text>
                                </Box>

                                {/* Exam Details */}
                                <Box style={{ marginBottom: '10px' }}>
                                    <Text style={{ fontSize: '13px', color: '#666', marginBottom: '3px' }}>
                                        {exam.description}
                                    </Text>
                                    <Text style={{ fontSize: '12px', color: '#888' }}>
                                        {exam.instructions}
                                    </Text>
                                </Box>

                                {/* Exam Stats */}
                                <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '10px', marginBottom: '10px' }}>
                                    <Box>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>Thời gian làm bài</Text>
                                        <Text bold style={{ fontSize: '14px' }}>{exam.timeLimit} phút</Text>
                                    </Box>
                                    <Box>
                                        <Text style={{ fontSize: '12px', color: '#666' }}>Số câu hỏi</Text>
                                        <Text bold style={{ fontSize: '14px' }}>{exam.questionsPerAttempt}/{exam.totalQuestions}</Text>
                                    </Box>
                                </Box>

                                {/* Student Stats */}
                                <Box style={{ backgroundColor: '#f0f8ff', borderRadius: '8px', padding: '10px', marginBottom: '10px' }}>
                                    <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Thống kê làm bài</Text>
                                    <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '10px' }}>
                                        <Box style={{ textAlign: 'center' }}>
                                            <Text bold style={{ fontSize: '16px', color: '#0068ff' }}>
                                                {exam.stats?.totalStudents || 0}
                                            </Text>
                                            <Text style={{ fontSize: '11px', color: '#666' }}>Học sinh</Text>
                                        </Box>
                                        <Box style={{ textAlign: 'center' }}>
                                            <Text bold style={{ fontSize: '16px', color: '#FF9800' }}>
                                                {exam.stats?.totalAttempts || 0}
                                            </Text>
                                            <Text style={{ fontSize: '11px', color: '#666' }}>Lượt thi</Text>
                                        </Box>
                                        <Box style={{ textAlign: 'center' }}>
                                            <Text bold style={{ fontSize: '16px', color: '#4CAF50' }}>
                                                {exam.stats?.totalQuestions || 0}
                                            </Text>
                                            <Text style={{ fontSize: '11px', color: '#666' }}>Câu hỏi</Text>
                                        </Box>
                                    </Box>
                                </Box>

                                {/* Classes */}
                                {exam.classes && exam.classes.length > 0 && (
                                    <Box style={{ marginBottom: '10px' }}>
                                        <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>Lớp học</Text>
                                        <Box flex style={{ gap: '5px', flexWrap: 'wrap' }}>
                                            {exam.classes.map((cls) => (
                                                <Box key={cls._id} style={{
                                                    backgroundColor: '#f0f0f0',
                                                    padding: '2px 6px',
                                                    borderRadius: '4px',
                                                    fontSize: '11px'
                                                }}>
                                                    {cls.name} ({cls.studentCount} HS)
                                                </Box>
                                            ))}
                                        </Box>
                                    </Box>
                                )}

                                {/* Date Info */}
                                <Box flex justifyContent="space-between" alignItems="center">
                                    <Text style={{ fontSize: '12px', color: '#888' }}>
                                        Tạo: {formatDistanceToNow(new Date(exam.createdAt), { locale: vi, addSuffix: true })}
                                    </Text>
                                    <Text style={{ fontSize: '16px', color: '#0068ff' }}>→</Text>
                                </Box>
                            </Box>
                        ))}
                    </List>
                ) : (
                    <Box style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
                        <Text style={{ fontSize: '48px', marginBottom: '10px' }}>📝</Text>
                        <Text bold size="large" style={{ marginBottom: '5px' }}>Chưa có bài thi nào</Text>
                        <Text style={{ fontSize: '14px' }}>Tạo bài thi mới để bắt đầu chấm điểm</Text>
                    </Box>
                )}

                {/* Pagination */}
                {pagination.totalPages > 1 && (
                    <Box flex justifyContent="center" alignItems="center" style={{ marginTop: '20px', gap: '10px' }}>
                        <Button
                            onClick={() => handlePageChange(pagination.currentPage - 1)}
                            disabled={pagination.currentPage <= 1}
                            style={{
                                backgroundColor: pagination.currentPage <= 1 ? '#f0f0f0' : '#0068ff',
                                color: pagination.currentPage <= 1 ? '#999' : 'white'
                            }}
                        >
                            ←
                        </Button>
                        
                        <Text style={{ fontSize: '14px', color: '#666' }}>
                            Trang {pagination.currentPage} / {pagination.totalPages}
                        </Text>
                        
                        <Button
                            onClick={() => handlePageChange(pagination.currentPage + 1)}
                            disabled={pagination.currentPage >= pagination.totalPages}
                            style={{
                                backgroundColor: pagination.currentPage >= pagination.totalPages ? '#f0f0f0' : '#0068ff',
                                color: pagination.currentPage >= pagination.totalPages ? '#999' : 'white'
                            }}
                        >
                            →
                        </Button>
                    </Box>
                )}
            </Box>

            <BottomNavigationEdu />
        </Box>
    );
};

export default TeacherGrading; 