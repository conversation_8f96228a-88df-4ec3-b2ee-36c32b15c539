import { useState, useCallback, useContext, useMemo, useEffect } from 'react';
import { Box, Text, Modal, useNavigate } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import ContentLoading from '../components/ContentLoading';
import LoadingIndicator from '../components/LoadingIndicator';
import useApiCache from '../hooks/useApiCache';
import HeaderSpacer from '../components/HeaderSpacer';

const Directory = () => {
    const { user } = useContext(AuthContext);
    const navigate = useNavigate();
    const [searchTerm, setSearchTerm] = useState('');
    const [activeFilter, setActiveFilter] = useState('all');
    const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
    const [showClassFilter, setShowClassFilter] = useState(false);
    const [activeClass, setActiveClass] = useState('');
    const [activeClassId, setActiveClassId] = useState('');
    const [activeGroup, setActiveGroup] = useState('');
    const [activeLetter, setActiveLetter] = useState('A');
    const [favoriteOnly, setFavoriteOnly] = useState(false);
    const [confirmModal, setConfirmModal] = useState({
        visible: false,
        title: '',
        message: '',
        onConfirm: null,
        confirmText: '',
        cancelText: ''
    });

    // State để lưu trữ danh sách lớp học
    const [classes, setClasses] = useState([]);
    // State để lưu trữ thông tin chi tiết lớp học
    const [classDetail, setClassDetail] = useState(null);

    // Hàm lấy danh sách lớp học
    const fetchClasses = useCallback(async () => {
        if (!user) return [];
        try {
            const response = await authApi.get('/directory/classes');
            if (response.data.success) {
                const classesData = response.data.data || [];
                setClasses(classesData);

                // Nếu chưa có lớp học active, chọn lớp đầu tiên
                if (classesData.length > 0 && !activeClassId) {
                    setActiveClassId(classesData[0].id);
                    setActiveClass(classesData[0].name);
                }

                return classesData;
            }
            return [];
        } catch (err) {
            console.error('Error fetching classes:', err);
            return [];
        }
    }, [user, activeClassId]);

    // Hàm lấy thông tin chi tiết lớp học
    const fetchClassDetail = useCallback(async (classId) => {
        if (!classId) return null;
        try {
            const response = await authApi.get(`/directory/class/${classId}`);
            if (response.data.success) {
                const detailData = response.data.data || null;
                setClassDetail(detailData);
                return detailData;
            }
            return null;
        } catch (err) {
            console.error('Error fetching class detail:', err);
            return null;
        }
    }, []);

    // Sử dụng useApiCache để lấy danh sách lớp học
    const {
        data: classesData = [],
        loading: classesApiLoading,
        refetch: refetchClasses
    } = useApiCache(fetchClasses, [user], {
        cacheKey: `directory_classes_${user?._id}`,
        enabled: !!user,
        cacheTime: 5 * 60 * 1000, // Cache 5 phút
    });

    // Sử dụng useApiCache để lấy chi tiết lớp học
    const {
        data: classDetailData = null,
        loading: classDetailApiLoading
    } = useApiCache(() => fetchClassDetail(activeClassId), [activeClassId], {
        cacheKey: `directory_class_detail_${activeClassId}`,
        enabled: !!activeClassId,
        cacheTime: 0, // Không cache dữ liệu chi tiết lớp học để luôn lấy dữ liệu mới
        staleTime: 0, // Luôn coi dữ liệu là cũ để gọi API mới
    });

    // Tải dữ liệu ban đầu khi component mount
    useEffect(() => {
        if (user) {
            refetchClasses();
        }
    }, [user, refetchClasses]);

    // Cập nhật danh sách lớp học từ API
    useEffect(() => {
        if (classesData && classesData.length > 0) {
            setClasses(classesData);

            // Nếu chưa có lớp học active, chọn lớp đầu tiên
            if (!activeClassId) {
                setActiveClassId(classesData[0].id);
                setActiveClass(classesData[0].name);
            }
        }
    }, [classesData, activeClassId]);

    // Cập nhật chi tiết lớp học từ API
    useEffect(() => {
        if (classDetailData) {
            setClassDetail(classDetailData);
        }
    }, [classDetailData]);

    // Xử lý khi chọn lớp học
    const handleClassSelect = (classId, className) => {
        console.log(`Selecting class: ${className} (${classId})`);
        setActiveClassId(classId);
        setActiveClass(className);

        // Đặt classDetail về null trước khi gọi API để tránh hiển thị dữ liệu cũ
        setClassDetail(null);

        // Gọi API để lấy dữ liệu mới
        fetchClassDetail(classId).then(data => {
            console.log("Fetched class detail:", data);
        }).catch(err => {
            console.error("Error fetching class detail:", err);
        });
    };

    // Dữ liệu giáo viên từ API
    const teachersData = useMemo(() => {
        if (!classDetail || !classDetail.teachers) return [];
        return classDetail.teachers;
    }, [classDetail]);

    // Dữ liệu học sinh từ API
    const studentsData = useMemo(() => {
        if (!classDetail || !classDetail.students) return [];
        return classDetail.students;
    }, [classDetail]);

    // Lọc dữ liệu dựa trên bộ lọc
    const filteredData = useMemo(() => {
        let filtered = [];

        // Lọc theo loại (giáo viên/học sinh)
        if (activeFilter === 'all') {
            filtered = [...teachersData, ...studentsData];
        } else if (activeFilter === 'teachers') {
            filtered = [...teachersData];
        } else if (activeFilter === 'students') {
            filtered = [...studentsData];
        } else if (activeFilter === 'class') {
            // Nếu đã có dữ liệu chi tiết lớp học, sử dụng dữ liệu đó
            if (classDetail) {
                // Hiển thị cả giáo viên và học sinh của lớp
                const teachers = classDetail.teachers || [];
                const students = classDetail.students || [];
                filtered = [...teachers, ...students];
            } else {
                filtered = [];
            }
        }

        // Lọc theo từ khóa tìm kiếm
        if (searchTerm) {
            filtered = filtered.filter(item =>
                item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (item.displayMeta && item.displayMeta.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (item.class && item.class.toLowerCase().includes(searchTerm.toLowerCase()))
            );
        }

        // Lọc theo group nếu có
        if (activeGroup) {
            filtered = filtered.filter(item => item.group === activeGroup);
        }

        // Lọc theo yêu thích
        if (favoriteOnly) {
            filtered = filtered.filter(item => item.favorite);
        }

        return filtered;
    }, [teachersData, studentsData, activeFilter, searchTerm, favoriteOnly, classDetail, activeGroup]);

    // Hiển thị modal xác nhận
    const showConfirmModal = (title, message, onConfirm, confirmText = 'Xác nhận', cancelText = 'Hủy') => {
        setConfirmModal({
            visible: true,
            title,
            message,
            onConfirm,
            confirmText,
            cancelText
        });
    };

    // Đóng modal xác nhận
    const closeConfirmModal = () => {
        setConfirmModal(prev => ({
            ...prev,
            visible: false
        }));
    };

    // Xử lý gọi điện
    const handleCall = (contact) => {
        showConfirmModal(
            'Gọi điện',
            `Bạn muốn gọi điện cho ${contact.name}?`,
            () => {
                // Xử lý gọi điện
                closeConfirmModal();
            },
            'Gọi',
            'Hủy'
        );
    };

    // Xử lý nhắn tin
    const handleMessage = (contact) => {
        // Chuyển đến trang nhắn tin
        navigate(`/message/${contact.id}`);
    };

    // Chuyển đến trang chi tiết liên hệ
    const viewContactDetail = (contact) => {
        navigate(`/contact/${contact.id}`);
    };

    // Xử lý thay đổi trạng thái yêu thích
    const handleToggleFavorite = async (contact, e) => {
        e.stopPropagation(); // Ngăn sự kiện lan truyền lên phần tử cha

        try {
            // Cập nhật UI trước để tạo trải nghiệm mượt mà hơn
            // Tạo bản sao của contact với trạng thái favorite đã đảo ngược
            const updatedContact = { ...contact, favorite: !contact.favorite };

            // Cập nhật tạm thời trong state local
            if (classDetail) {
                // Cập nhật trong danh sách giáo viên
                if (updatedContact.role.includes('teacher') && classDetail.teachers) {
                    const updatedTeachers = classDetail.teachers.map(t =>
                        t.id === updatedContact.id ? updatedContact : t
                    );
                    setClassDetail({...classDetail, teachers: updatedTeachers});
                }

                // Cập nhật trong danh sách học sinh
                if (updatedContact.role.includes('student') && classDetail.students) {
                    const updatedStudents = classDetail.students.map(s =>
                        s.id === updatedContact.id ? updatedContact : s
                    );
                    setClassDetail({...classDetail, students: updatedStudents});
                }
            }

            // Gọi API để cập nhật trạng thái yêu thích
            const response = await authApi.post(`/directory/favorite/${contact.id}`, {
                favorite: !contact.favorite
            });

            if (response.data.success) {
                // Cập nhật lại dữ liệu từ server để đảm bảo đồng bộ
                if (activeFilter === 'class' && activeClassId) {
                    fetchClassDetail(activeClassId);
                } else {
                    refetchClasses();
                }

                // Hiển thị thông báo nhỏ
                showConfirmModal(
                    contact.favorite ? 'Đã bỏ yêu thích' : 'Đã thêm vào yêu thích',
                    `${contact.name} đã ${contact.favorite ? 'bị xóa khỏi' : 'được thêm vào'} danh sách yêu thích.`,
                    closeConfirmModal,
                    'Đóng'
                );
            }
        } catch (err) {
            console.error('Error toggling favorite:', err);
            // Hiển thị thông báo lỗi
            showConfirmModal(
                'Lỗi',
                'Không thể cập nhật trạng thái yêu thích. Vui lòng thử lại sau.',
                closeConfirmModal,
                'Đóng'
            );

            // Khôi phục lại UI nếu có lỗi
            if (activeFilter === 'class' && activeClassId) {
                fetchClassDetail(activeClassId);
            } else {
                refetchClasses();
            }
        }
    };

    // Lấy họ (lastname) từ tên đầy đủ
    const getLastName = (fullName) => {
        // Tách tên thành các phần
        const nameParts = fullName.trim().split(' ');
        // Lấy phần cuối cùng (họ trong tiếng Việt)
        return nameParts[nameParts.length - 1];
    };

    // Nhóm danh sách theo chữ cái đầu tiên của họ (cho chế độ xem danh sách)
    const groupedByAlphabet = useMemo(() => {
        const grouped = {};

        filteredData.forEach(item => {
            // Lấy họ và chữ cái đầu tiên của họ
            const lastName = getLastName(item.name);
            const firstLetter = lastName.charAt(0).toUpperCase();

            if (!grouped[firstLetter]) {
                grouped[firstLetter] = [];
            }
            grouped[firstLetter].push(item);
        });

        // Sắp xếp các nhóm theo thứ tự bảng chữ cái
        return Object.keys(grouped)
            .sort()
            .reduce((obj, key) => {
                obj[key] = grouped[key];
                return obj;
            }, {});
    }, [filteredData]);

    // Danh sách các chữ cái xuất hiện trong danh sách
    const alphabetList = useMemo(() => {
        return Object.keys(groupedByAlphabet);
    }, [groupedByAlphabet]);

    // Nhóm giáo viên và học sinh (cho chế độ xem lưới)
    const groupedByType = useMemo(() => {
        if (activeFilter === 'all' || activeFilter === 'class') {
            return {
                teachers: filteredData.filter(item => item.role.includes('teacher')),
                students: filteredData.filter(item => item.role.includes('student'))
            };
        } else if (activeFilter === 'teachers') {
            return {
                teachers: filteredData,
                students: []
            };
        } else {
            return {
                teachers: [],
                students: filteredData
            };
        }
    }, [filteredData, activeFilter]);

    // Xử lý khi filter tab được nhấp
    const handleFilterTabClick = (filter) => {
        setActiveFilter(filter);
        setShowClassFilter(filter === 'class');

        // Nếu chọn tab lớp học, luôn tải lại danh sách lớp học
        if (filter === 'class') {
            console.log("Fetching classes for class tab");
            refetchClasses();

            // Nếu đã có lớp học active, tải chi tiết lớp học
            if (activeClassId) {
                console.log(`Fetching details for active class: ${activeClass} (${activeClassId})`);
                // Đặt classDetail về null trước khi gọi API để tránh hiển thị dữ liệu cũ
                setClassDetail(null);
                fetchClassDetail(activeClassId);
            }
        }
    };

    return (
        <Box
            className="container"
            style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '80px' }}
        >
            <HeaderEdu title="Danh Bạ" showBackButton={true} />
            <HeaderSpacer />
            <Box className="page-container" style={{ flex: 1, display: 'flex', flexDirection: 'column', marginTop: 0, paddingTop: 0 }}>
                {/* Thanh tìm kiếm */}
                <Box className="search-container" style={{ backgroundColor: 'white', padding: '5px 10px', position: 'relative', boxShadow: '0 1px 2px rgba(0,0,0,0.05)' }}>
                    <Box className="search-box" style={{ display: 'flex', alignItems: 'center', backgroundColor: '#f0f6ff', borderRadius: '8px', padding: '6px 10px', marginBottom: '8px' }}>
                        <Text className="search-icon" style={{ color: '#666', marginRight: '8px', fontSize: '16px' }}>🔍</Text>
                        <input
                            type="text"
                            className="search-input"
                            placeholder="Tìm kiếm học sinh, giáo viên..."
                            style={{ border: 'none', backgroundColor: 'transparent', flex: 1, padding: '4px', outline: 'none', fontSize: '14px' }}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </Box>

                    <Box className="filter-tabs" style={{ display: 'flex', gap: '8px', overflowX: 'auto', paddingBottom: '5px', scrollbarWidth: 'none' }}>
                        {['all', 'teachers', 'students', 'class'].map((filter) => (
                            <Box
                                key={filter}
                                className={`filter-tab ${activeFilter === filter ? 'active' : ''}`}
                                style={{
                                    padding: '6px 12px',
                                    backgroundColor: activeFilter === filter ? '#0068ff' : '#f0f6ff',
                                    borderRadius: '16px',
                                    fontSize: '13px',
                                    whiteSpace: 'nowrap',
                                    cursor: 'pointer',
                                    color: activeFilter === filter ? 'white' : 'black',
                                    border: `1px solid ${activeFilter === filter ? '#0068ff' : '#e0e0e0'}`,
                                    boxShadow: activeFilter === filter ? '0 2px 4px rgba(0, 104, 255, 0.3)' : 'none'
                                }}
                                onClick={() => handleFilterTabClick(filter)}
                            >
                                {filter === 'all' ? 'Tất cả' :
                                    filter === 'teachers' ? 'Giáo viên' :
                                        filter === 'students' ? 'Học sinh' : 'Lớp học'}
                            </Box>
                        ))}
                    </Box>

                    {showClassFilter && (
                        <Box className="class-filter" style={{ padding: '8px 10px', backgroundColor: 'white', borderTop: '1px solid #f0f0f0' }}>
                            {classesApiLoading ? (
                                <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                                    <LoadingIndicator />
                                </Box>
                            ) : (
                                <Box className="class-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '8px' }}>
                                    {classes.map((cls) => (
                                        <Box
                                            key={cls.id}
                                            className={`class-item ${activeClassId === cls.id ? 'active' : ''}`}
                                            style={{
                                                backgroundColor: activeClassId === cls.id ? '#0068ff' : '#f0f6ff',
                                                padding: '8px 5px',
                                                borderRadius: '8px',
                                                textAlign: 'center',
                                                fontSize: '13px',
                                                cursor: 'pointer',
                                                color: activeClassId === cls.id ? 'white' : 'black'
                                            }}
                                            onClick={() => handleClassSelect(cls.id, cls.name)}
                                        >
                                            {cls.name}
                                        </Box>
                                    ))}
                                </Box>
                            )}
                        </Box>
                    )}
                </Box>

                {/* Chế độ xem */}
                <Box className="view-toggle" style={{ display: 'flex', justifyContent: 'center', backgroundColor: 'white', padding: '8px 15px', borderRadius: '8px', margin: '10px 15px 10px' }}>
                    <Box
                        className={`toggle-button ${viewMode === 'grid' ? 'active' : ''}`}
                        style={{
                            flex: 1,
                            padding: '8px 0',
                            textAlign: 'center',
                            fontSize: '14px',
                            cursor: 'pointer',
                            borderRight: '1px solid #f0f0f0',
                            color: viewMode === 'grid' ? '#0068ff' : 'black',
                            fontWeight: viewMode === 'grid' ? 'bold' : 'normal'
                        }}
                        onClick={() => setViewMode('grid')}
                    >
                        Hiển thị lưới
                    </Box>
                    <Box
                        className={`toggle-button ${viewMode === 'list' ? 'active' : ''}`}
                        style={{
                            flex: 1,
                            padding: '8px 0',
                            textAlign: 'center',
                            fontSize: '14px',
                            cursor: 'pointer',
                            color: viewMode === 'list' ? '#0068ff' : 'black',
                            fontWeight: viewMode === 'list' ? 'bold' : 'normal'
                        }}
                        onClick={() => setViewMode('list')}
                    >
                        Hiển thị danh sách
                    </Box>
                </Box>

                {/* Bộ lọc */}
                <Box className="filter-container" style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px', padding: '0 15px' }}>
                    <Box>
                        {activeGroup && (
                            <Box
                                className="group-filter"
                                style={{
                                    backgroundColor: '#0068ff',
                                    padding: '8px 15px',
                                    borderRadius: '20px',
                                    fontSize: '14px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    cursor: 'pointer',
                                    color: 'white'
                                }}
                                onClick={() => setActiveGroup('')}
                            >
                                <span>Nhóm {activeGroup}</span>
                                <span style={{ marginLeft: '5px', fontSize: '16px' }}>×</span>
                            </Box>
                        )}
                    </Box>
                    <Box>
                        <Box
                            className={`favorite-filter ${favoriteOnly ? 'active' : ''}`}
                            style={{
                                backgroundColor: favoriteOnly ? '#FFD700' : 'white',
                                padding: '8px 15px',
                                borderRadius: '20px',
                                fontSize: '14px',
                                display: 'flex',
                                alignItems: 'center',
                                cursor: 'pointer',
                                border: `1px solid ${favoriteOnly ? '#FFD700' : '#e0e0e0'}`,
                                color: favoriteOnly ? '#333' : 'black'
                            }}
                            onClick={() => setFavoriteOnly(!favoriteOnly)}
                        >
                            <span style={{ marginRight: '5px', color: favoriteOnly ? '#333' : '#FFD700', fontSize: '16px' }}>★</span>
                            <span>Yêu thích</span>
                        </Box>
                    </Box>
                </Box>

                {/* Nội dung chính */}
                {classDetailApiLoading ? (
                    <ContentLoading />
                ) : (
                    <>
                        {/* Chế độ xem lưới */}
                        {viewMode === 'grid' && (
                            <Box className="directory-container" style={{ padding: '0 15px 15px' }}>
                                {/* Hiển thị giáo viên */}
                                {groupedByType.teachers.length > 0 && (
                                    <Box className="directory-section" style={{ marginBottom: '20px' }}>
                                        <Box className="section-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                                            <Text className="section-title" style={{ fontSize: '18px', fontWeight: 'bold' }}>
                                                Giáo viên {activeFilter === 'class' ? activeClass : ''}
                                            </Text>
                                        </Box>
                                        <Box className="contact-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '15px' }}>
                                            {groupedByType.teachers.map((teacher) => (
                                                <Box
                                                    key={teacher.id}
                                                    className="contact-card"
                                                    style={{
                                                        backgroundColor: 'white',
                                                        borderRadius: '10px',
                                                        overflow: 'hidden',
                                                        boxShadow: '0 2px 5px rgba(0, 0, 0, 0.08)',
                                                        display: 'flex',
                                                        flexDirection: 'column'
                                                    }}
                                                    onClick={() => viewContactDetail(teacher)}
                                                >
                                                    <Box className="contact-info" style={{ padding: '12px', display: 'flex', alignItems: 'center' }}>
                                                        <Box className="contact-avatar" style={{ width: '55px', height: '55px', borderRadius: '50%', overflow: 'hidden', marginRight: '12px', flexShrink: 0, border: '2px solid #f0f0f0', position: 'relative' }}>
                                                            <Box
                                                                className={`avatar-image ${teacher.gender}`}
                                                                style={{
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    width: '100%',
                                                                    height: '100%',
                                                                    fontSize: '40px',
                                                                    background: teacher.gender === 'male' ? 'linear-gradient(135deg, #c4e3ff, #6cacff)' : 'linear-gradient(135deg, #ffd6ec, #ff8bc2)',
                                                                    color: teacher.gender === 'male' ? '#0068ff' : '#e6007e'
                                                                }}
                                                            >
                                                                {teacher.gender === 'male' ? '👨' : '👩'}
                                                            </Box>
                                                            <Box
                                                                className="favorite-badge"
                                                                style={{
                                                                    position: 'absolute',
                                                                    top: '5px',
                                                                    right: '5px',
                                                                    width: '24px',
                                                                    height: '24px',
                                                                    backgroundColor: teacher.favorite ? '#FFD700' : 'rgba(255, 255, 255, 0.8)',
                                                                    borderRadius: '50%',
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    fontSize: '14px',
                                                                    color: teacher.favorite ? '#333' : '#aaa',
                                                                    cursor: 'pointer',
                                                                    boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                                                                }}
                                                                onClick={(e) => handleToggleFavorite(teacher, e)}
                                                            >
                                                                ★
                                                            </Box>
                                                        </Box>
                                                        <Box className="contact-details" style={{ flex: 1 }}>
                                                            <Text className="contact-name" style={{ fontWeight: 'bold', fontSize: '16px', marginBottom: '2px', color: '#333' }}>
                                                                {teacher.name}
                                                            </Text>
                                                            <Text className="contact-role" style={{ fontSize: '12px', color: '#0068ff', fontWeight: 500, marginBottom: '3px' }}>
                                                                {teacher.displayRole}
                                                            </Text>
                                                            <Text className="contact-meta" style={{ fontSize: '12px', color: '#666' }}>
                                                                {teacher.displayMeta}
                                                            </Text>
                                                        </Box>
                                                    </Box>
                                                    <Box className="contact-actions" style={{ display: 'flex', borderTop: '1px solid #f0f0f0' }}>
                                                        <Box
                                                            className="action-button"
                                                            style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '10px 0', cursor: 'pointer', fontSize: '12px', color: '#666', borderRight: '1px solid #f0f0f0' }}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleCall(teacher);
                                                            }}
                                                        >
                                                            <span className="action-icon" style={{ marginRight: '5px', fontSize: '14px' }}>📞</span>
                                                            <span>Gọi điện</span>
                                                        </Box>
                                                        <Box
                                                            className="action-button"
                                                            style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '10px 0', cursor: 'pointer', fontSize: '12px', color: '#666' }}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleMessage(teacher);
                                                            }}
                                                        >
                                                            <span className="action-icon" style={{ marginRight: '5px', fontSize: '14px' }}>✉️</span>
                                                            <span>Nhắn tin</span>
                                                        </Box>
                                                    </Box>
                                                </Box>
                                            ))}
                                        </Box>
                                    </Box>
                                )}

                                {/* Hiển thị học sinh */}
                                {groupedByType.students.length > 0 && (
                                    <Box className="directory-section" style={{ marginBottom: '20px' }}>
                                        <Box className="section-header" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                                            <Text className="section-title" style={{ fontSize: '18px', fontWeight: 'bold' }}>
                                                Học sinh {activeFilter === 'class' ? activeClass : ''}
                                            </Text>
                                        </Box>
                                        <Box className="contact-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '15px' }}>
                                            {groupedByType.students.map((student) => (
                                                <Box
                                                    key={student.id}
                                                    className="contact-card"
                                                    style={{
                                                        backgroundColor: 'white',
                                                        borderRadius: '10px',
                                                        overflow: 'hidden',
                                                        boxShadow: '0 2px 5px rgba(0, 0, 0, 0.08)',
                                                        display: 'flex',
                                                        flexDirection: 'column'
                                                    }}
                                                    onClick={() => viewContactDetail(student)}
                                                >
                                                    <Box className="contact-info" style={{ padding: '12px', display: 'flex', alignItems: 'center' }}>
                                                        <Box className="contact-avatar" style={{ width: '55px', height: '55px', borderRadius: '50%', overflow: 'hidden', marginRight: '12px', flexShrink: 0, border: '2px solid #f0f0f0', position: 'relative' }}>
                                                            <Box
                                                                className={`avatar-image ${student.gender}`}
                                                                style={{
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    width: '100%',
                                                                    height: '100%',
                                                                    fontSize: '40px',
                                                                    background: student.gender === 'male' ? 'linear-gradient(135deg, #c4e3ff, #6cacff)' : 'linear-gradient(135deg, #ffd6ec, #ff8bc2)',
                                                                    color: student.gender === 'male' ? '#0068ff' : '#e6007e'
                                                                }}
                                                            >
                                                                {student.gender === 'male' ? '👨' : '👩'}
                                                            </Box>
                                                            <Box
                                                                className="favorite-badge"
                                                                style={{
                                                                    position: 'absolute',
                                                                    top: '5px',
                                                                    right: '5px',
                                                                    width: '24px',
                                                                    height: '24px',
                                                                    backgroundColor: student.favorite ? '#FFD700' : 'rgba(255, 255, 255, 0.8)',
                                                                    borderRadius: '50%',
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    fontSize: '14px',
                                                                    color: student.favorite ? '#333' : '#aaa',
                                                                    cursor: 'pointer',
                                                                    boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                                                                }}
                                                                onClick={(e) => handleToggleFavorite(student, e)}
                                                            >
                                                                ★
                                                            </Box>
                                                        </Box>
                                                        <Box className="contact-details" style={{ flex: 1 }}>
                                                            <Text className="contact-name" style={{ fontWeight: 'bold', fontSize: '16px', marginBottom: '2px', color: '#333' }}>
                                                                {student.name}
                                                            </Text>
                                                            <Text className="contact-role" style={{ fontSize: '12px', color: '#0068ff', fontWeight: 500, marginBottom: '3px' }}>
                                                                {student.displayRole}
                                                            </Text>
                                                            <Text
                                                                className="contact-meta"
                                                                style={{ fontSize: '12px', color: '#666' }}
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    if (student.group) {
                                                                        setActiveGroup(student.group === activeGroup ? '' : student.group);
                                                                    }
                                                                }}
                                                            >
                                                                Lớp {student.class} {student.group ? (
                                                                    <span
                                                                        style={{
                                                                            color: student.group === activeGroup ? '#0068ff' : '#666',
                                                                            fontWeight: student.group === activeGroup ? 'bold' : 'normal',
                                                                            textDecoration: 'underline',
                                                                            cursor: 'pointer'
                                                                        }}
                                                                    >
                                                                        • Nhóm {student.group}
                                                                    </span>
                                                                ) : student.displayMeta ? `• ${student.displayMeta}` : ''}
                                                            </Text>
                                                        </Box>
                                                    </Box>
                                                    <Box className="contact-actions" style={{ display: 'flex', borderTop: '1px solid #f0f0f0' }}>
                                                        <Box
                                                            className="action-button"
                                                            style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '10px 0', cursor: 'pointer', fontSize: '12px', color: '#666', borderRight: '1px solid #f0f0f0' }}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleCall(student);
                                                            }}
                                                        >
                                                            <span className="action-icon" style={{ marginRight: '5px', fontSize: '14px' }}>📞</span>
                                                            <span>Gọi điện</span>
                                                        </Box>
                                                        <Box
                                                            className="action-button"
                                                            style={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '10px 0', cursor: 'pointer', fontSize: '12px', color: '#666' }}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleMessage(student);
                                                            }}
                                                        >
                                                            <span className="action-icon" style={{ marginRight: '5px', fontSize: '14px' }}>✉️</span>
                                                            <span>Nhắn tin</span>
                                                        </Box>
                                                    </Box>
                                                </Box>
                                            ))}
                                        </Box>
                                    </Box>
                                )}

                                {filteredData.length === 0 && (
                                    <Box className="empty-state" style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', padding: '30px 15px', textAlign: 'center' }}>
                                        <Text className="empty-icon" style={{ fontSize: '50px', marginBottom: '15px', color: '#ccc' }}>🔍</Text>
                                        <Text className="empty-title" style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '10px', color: '#666' }}>
                                            Không tìm thấy kết quả
                                        </Text>
                                        <Text className="empty-text" style={{ fontSize: '14px', color: '#888', maxWidth: '250px', lineHeight: '1.4' }}>
                                            Thử tìm kiếm với từ khóa khác hoặc thay đổi bộ lọc
                                        </Text>
                                    </Box>
                                )}
                            </Box>
                        )}

                        {/* Chế độ xem danh sách */}
                        {viewMode === 'list' && (
                            <Box className="list-view-container" style={{ padding: '0 15px 15px' }}>
                                {/* Danh sách alphabet */}
                                <Box className="alphabet-list" style={{ display: 'flex', flexWrap: 'wrap', gap: '5px', padding: '10px 15px', backgroundColor: 'white', borderRadius: '8px', justifyContent: 'center', marginBottom: '15px' }}>
                                    {alphabetList.map(letter => (
                                        <Box
                                            key={letter}
                                            className={`alphabet-item ${activeLetter === letter ? 'active' : ''}`}
                                            style={{
                                                width: '30px',
                                                height: '30px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                borderRadius: '50%',
                                                fontSize: '14px',
                                                cursor: 'pointer',
                                                backgroundColor: activeLetter === letter ? '#0068ff' : 'transparent',
                                                color: activeLetter === letter ? 'white' : 'black'
                                            }}
                                            onClick={() => setActiveLetter(letter)}
                                        >
                                            {letter}
                                        </Box>
                                    ))}
                                </Box>

                                {/* Hiển thị danh sách theo chữ cái */}
                                {Object.keys(groupedByAlphabet)
                                    .filter(letter => letter === activeLetter) // Chỉ hiển thị chữ cái đang được chọn
                                    .map(letter => (
                                    <Box key={letter} className="list-section" style={{ marginBottom: '20px' }}>
                                        <Box className="alphabet-index" style={{ backgroundColor: '#f0f6ff', padding: '8px 15px', fontSize: '16px', fontWeight: 'bold', color: '#0068ff', margin: '20px 0 10px', borderRadius: '5px' }}>
                                            {letter}
                                        </Box>
                                        {groupedByAlphabet[letter].map(item => (
                                            <Box
                                                key={item.id}
                                                className="contact-list-item"
                                                style={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    padding: '12px 15px',
                                                    borderBottom: '1px solid #f0f0f0',
                                                    backgroundColor: 'white',
                                                    cursor: 'pointer'
                                                }}
                                                onClick={() => viewContactDetail(item)}
                                            >
                                                <Box className="contact-avatar" style={{ width: '55px', height: '55px', borderRadius: '50%', overflow: 'hidden', marginRight: '15px', flexShrink: 0 }}>
                                                    <Box
                                                        className={`avatar-image ${item.gender}`}
                                                        style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            width: '100%',
                                                            height: '100%',
                                                            fontSize: '40px',
                                                            background: item.gender === 'male' ? 'linear-gradient(135deg, #c4e3ff, #6cacff)' : 'linear-gradient(135deg, #ffd6ec, #ff8bc2)',
                                                            color: item.gender === 'male' ? '#0068ff' : '#e6007e'
                                                        }}
                                                    >
                                                        {item.gender === 'male' ? '👨' : '👩'}
                                                    </Box>
                                                </Box>
                                                <Box className="contact-details">
                                                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2px' }}>
                                                        <Text className="contact-name" style={{ fontWeight: 'bold', fontSize: '16px', color: '#333' }}>
                                                            {item.name}
                                                        </Text>
                                                        <Box
                                                            className="favorite-badge"
                                                            style={{
                                                                width: '24px',
                                                                height: '24px',
                                                                backgroundColor: item.favorite ? '#FFD700' : 'rgba(240, 240, 240, 0.8)',
                                                                borderRadius: '50%',
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                fontSize: '14px',
                                                                color: item.favorite ? '#333' : '#aaa',
                                                                cursor: 'pointer',
                                                                marginLeft: '10px'
                                                            }}
                                                            onClick={(e) => handleToggleFavorite(item, e)}
                                                        >
                                                            ★
                                                        </Box>
                                                    </Box>
                                                    <Text
                                                        className="contact-meta"
                                                        style={{ fontSize: '12px', color: '#666' }}
                                                    >
                                                        {item.role.includes('teacher') ? (
                                                            `${item.displayRole} • ${item.displayMeta}`
                                                        ) : (
                                                            <span>
                                                                {`${item.displayRole} • Lớp ${item.class}`}
                                                                {item.group && (
                                                                    <span
                                                                        style={{
                                                                            color: item.group === activeGroup ? '#0068ff' : '#666',
                                                                            fontWeight: item.group === activeGroup ? 'bold' : 'normal',
                                                                            textDecoration: 'underline',
                                                                            cursor: 'pointer',
                                                                            marginLeft: '5px'
                                                                        }}
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            if (item.group) {
                                                                                setActiveGroup(item.group === activeGroup ? '' : item.group);
                                                                            }
                                                                        }}
                                                                    >
                                                                        • Nhóm {item.group}
                                                                    </span>
                                                                )}
                                                            </span>
                                                        )}
                                                    </Text>
                                                </Box>
                                            </Box>
                                        ))}
                                    </Box>
                                ))}

                                {filteredData.length === 0 && (
                                    <Box className="empty-state" style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', padding: '30px 15px', textAlign: 'center' }}>
                                        <Text className="empty-icon" style={{ fontSize: '50px', marginBottom: '15px', color: '#ccc' }}>🔍</Text>
                                        <Text className="empty-title" style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '10px', color: '#666' }}>
                                            Không tìm thấy kết quả
                                        </Text>
                                        <Text className="empty-text" style={{ fontSize: '14px', color: '#888', maxWidth: '250px', lineHeight: '1.4' }}>
                                            Thử tìm kiếm với từ khóa khác hoặc thay đổi bộ lọc
                                        </Text>
                                    </Box>
                                )}
                            </Box>
                        )}
                    </>
                )}
            </Box>

            {/* Modal Xác nhận */}
            <Modal
                visible={confirmModal.visible}
                title={confirmModal.title}
                onClose={closeConfirmModal}
                actions={[
                    ...(confirmModal.cancelText ? [{
                        text: confirmModal.cancelText,
                        onClick: closeConfirmModal,
                        style: { backgroundColor: '#f5f5f5', color: '#333' }
                    }] : []),
                    ...(confirmModal.confirmText ? [{
                        text: confirmModal.confirmText,
                        onClick: () => {
                            if (confirmModal.onConfirm) {
                                confirmModal.onConfirm();
                            }
                        },
                        style: {
                            backgroundColor: '#0068ff',
                            color: 'white'
                        }
                    }] : [])
                ]}
            >
                <Box style={{ padding: '30px 20px', textAlign: 'center' }}>
                    <Text style={{ fontSize: '16px', marginBottom: '15px', lineHeight: '1.5' }}>{confirmModal.message}</Text>
                </Box>
            </Modal>

            <BottomNavigationEdu active="directory" />
        </Box>
    );
};

export default Directory;