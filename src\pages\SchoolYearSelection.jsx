import React, { useContext, useEffect } from 'react';
import { Box, Text, Select, Button } from 'zmp-ui';
import { useNavigate } from 'zmp-ui';
import { useSchoolYear } from '../context/SchoolYearContext';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';

const { Option } = Select;

const SchoolYearSelection = () => {
    const navigate = useNavigate();
    const { schoolYears, selectedSchoolYear, setSelectedSchoolYear, loading } = useSchoolYear();
    const { user, fetchUser } = useContext(AuthContext);

    useEffect(() => {
        // Nếu đã chọn năm học, chuyển hướng đến trang chính
        if (selectedSchoolYear && user) {
            const isTeacher = user.role.includes('teacher') || user.role.includes('TEACHER');
            const redirectPath = isTeacher ? '/teacher' : '/student';
            navigate(redirectPath, { replace: true });
        }
    }, [selectedSchoolYear, user, navigate]);

    const handleContinue = async () => {
        if (selectedSchoolYear) {
            try {
                // Save selected school year to localStorage first
                localStorage.setItem('selectedSchoolYear', selectedSchoolYear);

                // Update the context state to ensure synchronization
                setSelectedSchoolYear(selectedSchoolYear);

                // Call API directly with the selected school year to get fresh data
                const response = await authApi.get(`/auth/me?schoolYear=${selectedSchoolYear}`);
                const userData = response.data;

                console.log('SchoolYearSelection: User data for year', selectedSchoolYear, ':', userData);

                if (!userData) {
                    alert('Có lỗi xảy ra khi lấy thông tin người dùng');
                    return;
                }

                // Update user data in AuthContext with fresh data
                await fetchUser();

                // Navigate based on role - always navigate, let the target page handle missing class info
                const isTeacher = userData.role.includes('teacher') || userData.role.includes('TEACHER');
                const redirectPath = isTeacher ? '/teacher' : '/student';

                // Store class info status for StudentEdu to check
                if (userData.role.includes('student') && !userData.class) {
                    localStorage.setItem('showNoClassModal', 'true');
                }

                navigate(redirectPath, { replace: true });
            } catch (error) {
                console.error('Error fetching user data:', error);
                alert('Có lỗi xảy ra khi lấy thông tin người dùng');
            }
        }
    };

    return (
        <Box
            className="container"
            style={{
                maxWidth: '480px',
                width: '100%',
                margin: '0 auto',
                backgroundColor: 'white',
                minHeight: 'calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom))',
                display: 'flex',
                flexDirection: 'column',
                padding: '20px',
            }}
        >
            {/* Header */}
            <Box
                className="header"
                style={{
                    backgroundColor: '#0068ff',
                    color: 'white',
                    padding: '30px 20px',
                    textAlign: 'center',
                    position: 'relative',
                    minHeight: '150px',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginBottom: '30px',
                    borderRadius: '15px',
                }}
            >
                <Text
                    className="welcome-text"
                    bold
                    size="xxLarge"
                    style={{ marginBottom: '10px' }}
                >
                    Chào mừng {user?.name || 'bạn'}!
                </Text>
                <Text
                    className="subtitle"
                    size="large"
                    style={{ opacity: 0.9 }}
                >
                    Vui lòng chọn năm học để tiếp tục
                </Text>
            </Box>

            {/* Main Content */}
            <Box
                className="main-content"
                flex
                flexDirection="column"
                style={{ flex: 1 }}
            >
                <Box
                    className="illustration"
                    style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        margin: '20px 0',
                    }}
                >
                    <img
                        src="/assets/images/logo-tunghia1.jpg"
                        alt="THPT Số 1 Tư Nghĩa"
                        style={{ maxWidth: '70%', height: 'auto' }}
                    />
                </Box>

                <Box className="form-group" style={{ marginBottom: '30px' }}>
                    <Text className="form-label" bold style={{ marginBottom: '15px', color: '#555', fontSize: '16px' }}>
                        Chọn năm học
                    </Text>
                    <Select
                        value={selectedSchoolYear}
                        onChange={(value) => setSelectedSchoolYear(value)}
                        placeholder="Chọn năm học"
                        loading={loading}
                        style={{ width: '100%' }}
                    >
                        {schoolYears.map(year => (
                            <Option key={year} value={year} title={year} />
                        ))}
                    </Select>
                </Box>

                <Button
                    fullWidth
                    variant="primary"
                    onClick={handleContinue}
                    disabled={!selectedSchoolYear || loading}
                    style={{ marginTop: '20px' }}
                >
                    Tiếp tục
                </Button>
            </Box>

            <Box className="footer" style={{ padding: '20px', textAlign: 'center', fontSize: '12px', color: '#888' }}>
                © 2025 THPT Số 1 Tư Nghĩa - Tất cả quyền được bảo lưu
            </Box>
        </Box>
    );
};

export default SchoolYearSelection; 