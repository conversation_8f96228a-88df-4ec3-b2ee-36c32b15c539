import React, { useState, useContext, useEffect } from 'react';
import { Box, useNavigate, Modal, Text } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import LoadingIndicator from '../components/LoadingIndicator';
import LeaveRequestForm from '../components/LeaveRequestForm';

const TeacherLeaveRequest = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [submitting, setSubmitting] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const [totalImages, setTotalImages] = useState(0);

    // Kiểm tra đăng nhập
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && !user.role.includes('teacher') && !user.role.includes('TEACHER')) {
            navigate('/teacher', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Upload single image to ImgBB
    const uploadImageToImgBB = async (file) => {
        const reader = new FileReader();
        const base64Promise = new Promise((resolve, reject) => {
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
        });
        reader.readAsDataURL(file);
        
        let base64Data = await base64Promise;
        base64Data = base64Data.split(',')[1];
        
        const formData = new FormData();
        formData.append('image', base64Data);
        
        const response = await fetch('https://api.imgbb.com/1/upload?expiration=600&key=da8361af32920799b8a882ccf6c3f9c3', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        if (!result.success) {
            throw new Error('Không thể tải lên hình ảnh');
        }
        
        return {
            data: result.data.url,
            width: result.data.width,
            height: result.data.height
        };
    };

    // Handle form submission
    const handleSubmit = async ({ error, data }) => {
        if (error) {
            setError(error);
            return;
        }

        setSubmitting(true);
        setError('');
        
        try {
            // Upload all images first
            const attachmentsToUpload = data.attachments;
            setTotalImages(attachmentsToUpload.length);
            
            const uploadedAttachments = [];
            for (let i = 0; i < attachmentsToUpload.length; i++) {
                try {
                    const uploadedImage = await uploadImageToImgBB(attachmentsToUpload[i].file);
                    uploadedAttachments.push(uploadedImage);
                    setUploadProgress(i + 1);
                } catch (err) {
                    console.error('Error uploading image:', err);
                    setError(`Lỗi khi tải lên ảnh thứ ${i + 1}. ${err.message}`);
                    setSubmitting(false);
                    return;
                }
            }

            const requestData = {
                studentId: user._id,
                startDate: data.startDate,
                endDate: data.endDate,
                sessions: data.sessions,
                reason: data.reason,
                attachments: uploadedAttachments,
                requestType: 'teacher'
            };

            await authApi.post('/leave-requests', requestData);
            setSuccess(true);
            
        } catch (err) {
            console.error('Error submitting leave request:', err);
            setError(err.response?.data?.message || 'Có lỗi xảy ra khi gửi đơn xin nghỉ');
        } finally {
            setSubmitting(false);
        }
    };

    if (authLoading) {
        return (
            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
                <LoadingIndicator />
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Xin phép nghỉ dạy" 
                showBackButton={true} 
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />
            
            <Box style={{ padding: '15px', flex: 1 }}>
                <LeaveRequestForm
                    type="teacher"
                    onSubmit={handleSubmit}
                    loading={submitting}
                    error={error}
                    note="Lưu ý: Đơn xin nghỉ dạy cần được Ban Giám Hiệu phê duyệt trước khi có hiệu lực."
                />
            </Box>

            {/* Success Modal */}
            <Modal
                visible={success}
                title="Thành công"
                onClose={() => {
                    setSuccess(false);
                    navigate(-1);
                }}
                actions={[
                    {
                        text: 'Đóng',
                        close: true,
                        onClick: () => navigate(-1)
                    }
                ]}
            >
                <Box style={{ padding: '20px', textAlign: 'center' }}>
                    <Text style={{ fontSize: '48px', marginBottom: '10px' }}>✅</Text>
                    <Text.Title size="large" style={{ marginBottom: '10px' }}>
                        Gửi đơn thành công!
                    </Text.Title>
                    <Text style={{ color: '#666' }}>
                        Đơn xin nghỉ dạy của bạn đã được gửi và đang chờ duyệt từ Ban Giám Hiệu.
                    </Text>
                </Box>
            </Modal>

            <BottomNavigationEdu />
        </Box>
    );
};

export default TeacherLeaveRequest; 