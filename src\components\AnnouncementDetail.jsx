import React from 'react';
import { Box, Text, Modal } from 'zmp-ui';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';

const AnnouncementDetail = ({
    visible,
    onClose,
    announcement,
    onBookmark,
    isBookmarked,
    onViewReadStatus,
    mode
}) => {
    if (!announcement) return null;

    return (
        <Modal
            visible={visible}
            title="Chi tiết thông báo"
            onClose={onClose}
            actions={[
                {
                    text: isBookmarked ? '⭐ Đã lưu' : '☆ Lưu',
                    onClick: () => onBookmark(announcement._id || announcement.id)
                },
                ...(mode === 'sent' && announcement.readStats ? [{
                    text: '👥 Xem ai đã đọc',
                    onClick: () => {
                        onClose();
                        onViewReadStatus(announcement._id || announcement.id);
                    }
                }] : []),
                { text: 'Đóng', close: true }
            ]}
        >
            <Box p={4}>
                <Box style={{ marginBottom: '15px' }}>
                    <Text bold size="large" style={{ marginBottom: '8px' }}>
                        {announcement.title || 'Thông báo'}
                    </Text>
                    <Box flex alignItems="center" style={{ marginBottom: '10px' }}>
                        <Text style={{ fontSize: '14px', color: '#666' }}>
                            Từ: <Text bold>{announcement.sender?.name}</Text>
                        </Text>
                        <Text style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>
                            {formatDistanceToNow(new Date(announcement.createdAt), { locale: vi, addSuffix: true })}
                        </Text>
                    </Box>
                    {announcement.type && (
                        <Box style={{
                            backgroundColor: '#e8f0fe',
                            color: '#0068ff',
                            padding: '4px 8px',
                            borderRadius: '4px',
                            fontSize: '12px',
                            display: 'inline-block',
                            marginBottom: '10px'
                        }}>
                            {announcement.type === 'teacher_to_student' && 'Giáo viên gửi học sinh'}
                            {announcement.type === 'head_to_teacher' && 'Tổ trưởng gửi giáo viên'}
                            {announcement.type === 'principal_to_teacher' && 'Hiệu trưởng gửi giáo viên'}
                            {announcement.type === 'admin_to_all' && 'Ban giám hiệu gửi toàn trường'}
                        </Box>
                    )}
                </Box>
                <Box style={{
                    backgroundColor: '#f9f9f9',
                    padding: '15px',
                    borderRadius: '8px',
                    lineHeight: 1.6
                }}>
                    <Text style={{ whiteSpace: 'pre-wrap' }}>{announcement.content}</Text>
                </Box>
                {announcement.zaloConfig?.enabled && (
                    <Box style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f0f8ff', borderRadius: '6px' }}>
                        <Text style={{ fontSize: '12px', color: '#666' }}>
                            📱 Đã gửi qua Zalo: {announcement.zaloConfig.groupName}
                            {announcement.zaloConfig.sentAt && (
                                <Text> • {formatDistanceToNow(new Date(announcement.zaloConfig.sentAt), { locale: vi, addSuffix: true })}</Text>
                            )}
                        </Text>
                    </Box>
                )}
            </Box>
        </Modal>
    );
};

export default AnnouncementDetail; 