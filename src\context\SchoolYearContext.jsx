// src/context/SchoolYearContext.jsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { api } from '../utils/api';
import { AuthContext } from './AuthContext';
import useApiCache from '../hooks/useApiCache';

const SchoolYearContext = createContext();

export const SchoolYearProvider = ({ children }) => {
  const [selectedSchoolYear, setSelectedSchoolYear] = useState(() => {
    // Initialize from localStorage
    return localStorage.getItem('selectedSchoolYear') || '';
  });
  const { getToken } = useContext(AuthContext);

  // Save to localStorage whenever selectedSchoolYear changes
  useEffect(() => {
    if (selectedSchoolYear) {
      localStorage.setItem('selectedSchoolYear', selectedSchoolYear);
    } else {
      localStorage.removeItem('selectedSchoolYear');
    }
  }, [selectedSchoolYear]);

  console.log('SchoolYearProvider: Token available:', !!getToken());

  const fetchSchoolYears = () => {
    return api.get('/directory/school-years', {
      headers: {
        'x-auth-token': getToken()
      }
    });
  };

  const { data: schoolYears, loading, error } = useApiCache(
    fetchSchoolYears,
    [getToken()],
    {
      cacheKey: 'school-years',
      enabled: !!getToken(),
      onSuccess: (response) => {
        console.log('SchoolYearProvider: API call successful:', response.data);
        if (response.data.success && response.data.data.length > 0 && !selectedSchoolYear) {
          setSelectedSchoolYear(response.data.data[0]);
        }
      },
      onError: (error) => {
        console.error('SchoolYearProvider: API call failed:', error);
      }
    }
  );

  console.log('SchoolYearProvider: Current state:', {
    schoolYears: schoolYears?.data?.data,
    selectedSchoolYear,
    loading,
    error
  });

  const value = {
    schoolYears: schoolYears?.data?.data || [],
    selectedSchoolYear,
    setSelectedSchoolYear,
    loading
  };

  return (
    <SchoolYearContext.Provider value={value}>
      {children}
    </SchoolYearContext.Provider>
  );
};

export const useSchoolYear = () => useContext(SchoolYearContext);