import { Box, Text } from 'zmp-ui';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import LoadingIndicator from './LoadingIndicator';

const RecentAnnouncements = ({
    recentAnnouncements = [],
    unreadCount = 0,
    announcementsLoading = false,
    onAnnouncementClick,
    onViewAll,
    className = '',
    style = {}
}) => {
    return (
        <Box
            className={`section ${className}`}
            style={{
                backgroundColor: 'white',
                borderRadius: '16px',
                overflow: 'hidden',
                ...style
            }}
        >
            {/* Header */}
            <Box
                style={{
                    padding: '16px',
                    borderBottom: '1px solid #f0f0f0',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}
            >
                <Box flex alignItems="center" gap="8px">
                    <Box
                        style={{
                            width: '32px',
                            height: '32px',
                            borderRadius: '50%',
                            backgroundColor: '#e8f0fe',
                            color: '#0068ff',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontSize: '18px'
                        }}
                    >
                        📢
                    </Box>
                    <Text bold style={{ fontSize: '16px' }}>
                        Thông báo
                    </Text>
                    {unreadCount > 0 && (
                        <Box
                            style={{
                                backgroundColor: '#ff4444',
                                color: 'white',
                                fontSize: '12px',
                                fontWeight: '600',
                                padding: '2px 8px',
                                borderRadius: '10px',
                                minWidth: '20px',
                                height: '20px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                        >
                            {unreadCount}
                        </Box>
                    )}
                </Box>
                <Text
                    style={{
                        color: '#0068ff',
                        fontSize: '14px',
                        fontWeight: '500',
                        cursor: 'pointer'
                    }}
                    onClick={onViewAll}
                >
                    Xem tất cả
                </Text>
            </Box>

            {/* Content */}
            <Box style={{ padding: '8px' }}>
                {announcementsLoading ? (
                    <Box style={{ padding: '24px', display: 'flex', justifyContent: 'center' }}>
                        <LoadingIndicator />
                    </Box>
                ) : recentAnnouncements && recentAnnouncements.length > 0 ? (
                    <Box style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                        {recentAnnouncements.map((item) => (
                            <Box
                                key={item.id}
                                style={{
                                    backgroundColor: item.isRead ? 'white' : '#f0f8ff',
                                    borderRadius: '12px',
                                    padding: '12px',
                                    cursor: 'pointer',
                                    border: '1px solid #f0f0f0',
                                    position: 'relative',
                                    transition: 'all 0.2s ease',
                                    display: 'flex',
                                    gap: '12px',
                                    alignItems: 'flex-start'
                                }}
                                onClick={() => onAnnouncementClick(item)}
                            >
                                <Box
                                    style={{
                                        width: '36px',
                                        height: '36px',
                                        borderRadius: '50%',
                                        backgroundColor: '#e8f0fe',
                                        color: '#0068ff',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        fontSize: '18px',
                                        flexShrink: 0
                                    }}
                                >
                                    📢
                                </Box>
                                <Box style={{ flex: 1, minWidth: 0 }}>
                                    {/* Title */}
                                    <Text
                                        style={{
                                            fontSize: '15px',
                                            fontWeight: item.isRead ? '400' : '600',
                                            color: item.isRead ? '#333' : '#000',
                                            marginBottom: '8px',
                                            lineHeight: 1.4,
                                            display: '-webkit-box',
                                            WebkitLineClamp: 2,
                                            WebkitBoxOrient: 'vertical',
                                            overflow: 'hidden'
                                        }}
                                    >
                                        {item.title || (item.content ? item.content.substring(0, 60) + (item.content.length > 60 ? '...' : '') : '')}
                                    </Text>

                                    {/* Meta info */}
                                    <Box
                                        style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '8px'
                                        }}
                                    >
                                        <Text
                                            style={{
                                                fontSize: '12px',
                                                color: '#666',
                                                fontWeight: '500'
                                            }}
                                        >
                                            {item.sender?.name}
                                        </Text>
                                        <Text style={{ color: '#999', fontSize: '12px' }}>•</Text>
                                        <Text
                                            style={{
                                                fontSize: '12px',
                                                color: '#666'
                                            }}
                                        >
                                            {formatDistanceToNow(new Date(item.createdAt), { locale: vi, addSuffix: true })}
                                        </Text>
                                    </Box>
                                </Box>

                                {/* Unread indicator */}
                                {!item.isRead && (
                                    <Box
                                        style={{
                                            width: '8px',
                                            height: '8px',
                                            borderRadius: '50%',
                                            backgroundColor: '#0068ff',
                                            flexShrink: 0,
                                            marginTop: '4px'
                                        }}
                                    />
                                )}
                            </Box>
                        ))}
                    </Box>
                ) : (
                    <Box
                        style={{
                            padding: '24px',
                            textAlign: 'center',
                            backgroundColor: '#fafafa',
                            borderRadius: '12px',
                            margin: '8px'
                        }}
                    >
                        <Text style={{ color: '#666', fontSize: '14px' }}>
                            Chưa có thông báo mới
                        </Text>
                    </Box>
                )}
            </Box>
        </Box>
    );
};

export default RecentAnnouncements;