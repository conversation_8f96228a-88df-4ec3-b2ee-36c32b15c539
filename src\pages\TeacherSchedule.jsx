import React, { useEffect, useState, useContext, useCallback } from 'react';
import { Box, Text, useNavigate } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import LoadingIndicator from '../components/LoadingIndicator';
import useApiCache from '../hooks/useApiCache';

const TeacherSchedule = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
    const [currentYear, setCurrentYear] = useState(new Date().getFullYear());

    // API function for teacher classes
    const fetchTeacherClasses = useCallback(async () => {
        try {
            if (!user) return [];
            
            const response = await authApi.get('/directory/teacher/classes');
            if (response.data.success) {
                return response.data.data || [];
            }
            return [];
        } catch (error) {
            console.error('Error fetching teacher classes:', error);
            return []; // Luôn trả về array rỗng khi có lỗi
        }
    }, [user]);

    // Use useApiCache for teacher classes
    const {
        data: teacherClassesRaw = [],
        loading,
        error: apiError,
        refetch
    } = useApiCache(fetchTeacherClasses, [user?._id], {
        cacheKey: `teacher_schedule_classes_${user?._id}`,
        enabled: !!user && (user.role.includes('teacher') || user.role.includes('TEACHER')),
        cacheTime: 5 * 60 * 1000, // Cache 5 phút
    });

    // Ensure teacherClasses is always an array
    const teacherClasses = Array.isArray(teacherClassesRaw) ? teacherClassesRaw : [];

    const error = apiError ? 'Lỗi khi tải lịch dạy' : '';

    // Check authentication
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && !user.role.includes('teacher') && !user.role.includes('TEACHER')) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Generate calendar for current month
    const generateCalendar = () => {
        const firstDay = new Date(currentYear, currentMonth, 1);
        const lastDay = new Date(currentYear, currentMonth + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday
        
        const calendar = [];
        const today = new Date();
        
        for (let week = 0; week < 6; week++) {
            const weekDays = [];
            for (let day = 0; day < 7; day++) {
                const currentDate = new Date(startDate);
                currentDate.setDate(startDate.getDate() + week * 7 + day);
                
                const isCurrentMonth = currentDate.getMonth() === currentMonth;
                const isToday = currentDate.toDateString() === today.toDateString();
                
                // Check if there are classes on this day and count periods by session
                const dayName = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][currentDate.getDay()];
                
                // Collect all periods for this day
                const periodsOnDay = [];
                teacherClasses.forEach(cls => {
                    if (cls.teachingPeriods) {
                        cls.teachingPeriods.forEach(period => {
                            if (period.day === dayName) {
                                periodsOnDay.push(period.periodNumber);
                            }
                        });
                    }
                });

                // Count periods by session
                const morningPeriods = periodsOnDay.filter(p => p >= 1 && p <= 5).length;
                const afternoonPeriods = periodsOnDay.filter(p => p >= 6 && p <= 10).length;
                const hasClass = periodsOnDay.length > 0;
                
                weekDays.push({
                    date: currentDate.getDate(),
                    fullDate: new Date(currentDate),
                    isCurrentMonth,
                    isToday,
                    hasClass: hasClass && isCurrentMonth,
                    morningPeriods: isCurrentMonth ? morningPeriods : 0,
                    afternoonPeriods: isCurrentMonth ? afternoonPeriods : 0,
                    totalPeriods: isCurrentMonth ? periodsOnDay.length : 0
                });
            }
            calendar.push(weekDays);
        }
        
        return calendar;
    };

    // Get classes for a specific day
    const getClassesForDay = (dayName) => {
        const classes = [];
        teacherClasses.forEach(cls => {
            cls.teachingPeriods?.forEach(period => {
                if (period.day === dayName) {
                    classes.push({
                        className: cls.name,
                        subject: period.subject,
                        period: period.periodNumber,
                        room: period.room
                    });
                }
            });
        });
        return classes.sort((a, b) => a.period - b.period);
    };

    const monthNames = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];

    const weekDays = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];

    if (authLoading) {
        return (
            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
                <LoadingIndicator />
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Lịch dạy" 
                showBackButton={true} 
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />
            
            <Box style={{ padding: '15px' }}>
                {loading ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : error ? (
                    <Box style={{ textAlign: 'center', padding: '20px' }}>
                        <Text style={{ color: 'red' }}>{error}</Text>
                    </Box>
                ) : (
                    <>
                        {/* Month Navigation */}
                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px', backgroundColor: 'white', padding: '15px', borderRadius: '10px' }}>
                            <Text 
                                style={{ fontSize: '18px', cursor: 'pointer', color: '#0068ff' }}
                                onClick={() => {
                                    if (currentMonth === 0) {
                                        setCurrentMonth(11);
                                        setCurrentYear(currentYear - 1);
                                    } else {
                                        setCurrentMonth(currentMonth - 1);
                                    }
                                }}
                            >
                                ‹
                            </Text>
                            <Text bold size="large">
                                {monthNames[currentMonth]} {currentYear}
                            </Text>
                            <Text 
                                style={{ fontSize: '18px', cursor: 'pointer', color: '#0068ff' }}
                                onClick={() => {
                                    if (currentMonth === 11) {
                                        setCurrentMonth(0);
                                        setCurrentYear(currentYear + 1);
                                    } else {
                                        setCurrentMonth(currentMonth + 1);
                                    }
                                }}
                            >
                                ›
                            </Text>
                        </Box>

                        {/* Calendar */}
                        <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '20px' }}>
                            {/* Week days header */}
                            <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '5px', marginBottom: '10px' }}>
                                {weekDays.map((day, index) => (
                                    <Text key={index} style={{ textAlign: 'center', fontSize: '12px', color: '#666', fontWeight: 'bold' }}>
                                        {day}
                                    </Text>
                                ))}
                            </Box>
                            
                            {/* Calendar grid */}
                            {generateCalendar().map((week, weekIndex) => (
                                <Box key={weekIndex} style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '5px', marginBottom: '5px' }}>
                                    {week.map((day, dayIndex) => (
                                        <Box 
                                            key={dayIndex} 
                                            style={{ 
                                                minHeight: '40px',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                borderRadius: '5px',
                                                backgroundColor: day.isToday ? '#0068ff' : (day.hasClass ? '#e8f0fe' : 'transparent'),
                                                color: day.isToday ? 'white' : (day.isCurrentMonth ? 'black' : '#ccc'),
                                                border: day.hasClass && !day.isToday ? '1px solid #0068ff' : 'none',
                                                position: 'relative'
                                            }}
                                        >
                                            <Text style={{ fontSize: '14px', fontWeight: day.isToday ? 'bold' : 'normal' }}>
                                                {day.date}
                                            </Text>
                                            {/* Indicators for teaching periods */}
                                            {day.hasClass && (
                                                <Box style={{ display: 'flex', gap: '3px', marginTop: '2px' }}>
                                                    {/* Morning periods indicator (1-5) */}
                                                    {day.morningPeriods > 0 && (
                                                        <Box style={{ 
                                                            width: '4px', 
                                                            height: '4px', 
                                                            borderRadius: '50%', 
                                                            backgroundColor: day.isToday ? 'white' : '#4CAF50', // Green for morning
                                                            position: 'relative' 
                                                        }}>
                                                            {day.morningPeriods > 1 && (
                                                                <Text style={{ 
                                                                    position: 'absolute', 
                                                                    top: '-10px', 
                                                                    left: '50%', 
                                                                    transform: 'translateX(-50%)', 
                                                                    fontSize: '7px', 
                                                                    color: day.isToday ? 'white' : '#4CAF50', 
                                                                    fontWeight: 'bold' 
                                                                }}>
                                                                    {day.morningPeriods}
                                                                </Text>
                                                            )}
                                                        </Box>
                                                    )}
                                                    {/* Afternoon periods indicator (6-10) */}
                                                    {day.afternoonPeriods > 0 && (
                                                        <Box style={{ 
                                                            width: '4px', 
                                                            height: '4px', 
                                                            borderRadius: '50%', 
                                                            backgroundColor: day.isToday ? 'white' : '#FF9800', // Orange for afternoon
                                                            position: 'relative' 
                                                        }}>
                                                            {day.afternoonPeriods > 1 && (
                                                                <Text style={{ 
                                                                    position: 'absolute', 
                                                                    top: '-10px', 
                                                                    left: '50%', 
                                                                    transform: 'translateX(-50%)', 
                                                                    fontSize: '7px', 
                                                                    color: day.isToday ? 'white' : '#FF9800', 
                                                                    fontWeight: 'bold' 
                                                                }}>
                                                                    {day.afternoonPeriods}
                                                                </Text>
                                                            )}
                                                        </Box>
                                                    )}
                                                </Box>
                                            )}
                                        </Box>
                                    ))}
                                </Box>
                            ))}
                        </Box>

                        {/* Weekly Schedule Details */}
                        <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px' }}>
                            <Text bold size="large" style={{ marginBottom: '15px' }}>
                                Lịch dạy trong tuần
                            </Text>
                            
                            {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((dayEn, index) => {
                                const dayVN = weekDays[index];
                                const dayClasses = getClassesForDay(dayEn);
                                
                                return (
                                    <Box key={dayEn} style={{ marginBottom: '15px', paddingBottom: '15px', borderBottom: index < 6 ? '1px solid #f0f0f0' : 'none' }}>
                                        <Text bold style={{ marginBottom: '10px', color: '#0068ff' }}>
                                            {dayVN}
                                        </Text>
                                        {dayClasses.length > 0 ? (
                                            dayClasses.map((cls, clsIndex) => (
                                                <Box key={clsIndex} style={{ 
                                                    backgroundColor: '#f9f9f9', 
                                                    padding: '10px', 
                                                    borderRadius: '5px', 
                                                    marginBottom: '5px',
                                                    display: 'flex',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'center'
                                                }}>
                                                    <Box>
                                                        <Text bold style={{ fontSize: '14px' }}>
                                                            {cls.className} - {cls.subject}
                                                        </Text>
                                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                                            {cls.room}
                                                        </Text>
                                                    </Box>
                                                    <Text style={{ fontSize: '12px', color: '#0068ff', fontWeight: 'bold' }}>
                                                        Tiết {cls.period}
                                                    </Text>
                                                </Box>
                                            ))
                                        ) : (
                                            <Text style={{ fontSize: '14px', color: '#999', fontStyle: 'italic' }}>
                                                Không có lịch dạy
                                            </Text>
                                        )}
                                    </Box>
                                );
                            })}
                        </Box>
                    </>
                )}
            </Box>
            
            <BottomNavigationEdu />
        </Box>
    );
};

export default TeacherSchedule;
